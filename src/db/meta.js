export class MetaD1 {
  static async get(env, key) {
    if (!key) return null;

    const statement = env.DB.prepare('SELECT value FROM meta WHERE key = ?').bind(key);
    const result = await statement.first();

    if (!result) return null;

    try {
      return JSON.parse(result.value);
    } catch {
      return result.value;
    }
  }

  static async set(env, key, value) {
    if (!key) throw new Error('Missing key for meta update');

    const jsonValue = typeof value === 'string' ? value : JSON.stringify(value);

    const statement = env.DB.prepare(
      `INSERT INTO meta (key, value)
       VALUES (?, ?)
       ON CONFLICT(key) DO UPDATE SET value = ?, updated_at = datetime('now')`
    ).bind(key, jsonValue, jsonValue);

    await statement.run();
    return value;
  }

  static async delete(env, key) {
    if (!key) return false;

    const statement = env.DB.prepare('DELETE FROM meta WHERE key = ?').bind(key);
    const result = await statement.run();

    return result.changes > 0;
  }

  static async all(env) {
    const statement = env.DB.prepare('SELECT key, value FROM meta');
    const result = await statement.all();

    const meta = {};
    for (const row of result.results) {
      try {
        meta[row.key] = JSON.parse(row.value);
      } catch (error) {
        meta[row.key] = row.value;
      }
    }

    return meta;
  }
}

const AGENTS_KEY = 'agents:list';
export class Agents {
  static async getAgents(env) {
    const agentsList = MetaD1.get(env, AGENTS_KEY);
    return agentsList;
  }

  static async setAgents(env, agentsList) {
    if (!Array.isArray(agentsList)) {
      throw new Error('Agents list must be an array');
    }

    await MetaD1.set(env, AGENTS_KEY, agentsList);
    return agentsList;
  }
}
