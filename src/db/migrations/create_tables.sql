-- Applications table for storing application data
DROP TABLE IF EXISTS applications;

CREATE TABLE IF NOT EXISTS applications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL CHECK (length(uuid) > 0),
  `version` INTEGER NOT NULL CHECK (`version` >= 1),
  `status` TEXT NOT NULL CHECK (length(`status`) > 0),
  domain TEXT NOT NULL CHECK (length(domain) > 0),
  preQualifyFields TEXT NOT NULL CHECK (length(preQualifyFields) > 0), -- JSON stored as TEXT
  approvalAmount INTEGER DEFAULT 0 CHECK (approvalAmount >= 0),
  agent TEXT CHECK (agent IS NULL OR length(agent) > 0), -- <PERSON><PERSON><PERSON> stored as TEXT
  reason TEXT CHECK (reason IS NULL OR length(reason) > 0), -- denial reason if denied
  applicationFields TEXT CHECK (applicationFields IS NULL OR length(applicationFields) > 0), -- JSON stored as TEXT
  pandadoc TEXT CHECK (pandadoc IS NULL OR length(pandadoc) > 0), -- <PERSON><PERSON><PERSON> stored as TEXT
  utm TEXT CHECK (utm IS NULL OR length(utm) > 0), -- JSON stored as TEXT
  meta TEXT CHECK (meta IS NULL OR length(meta) > 0), -- JSON stored as TEXT
  fastTrack INTEGER NOT NULL DEFAULT 0 CHECK (fastTrack IN (0, 1)), -- BOOLEAN as INTEGER (0/1)
  salesforce_id VARCHAR(18) UNIQUE CHECK (salesforce_id IS NULL OR length(salesforce_id) = 18), -- Salesforce record ID of Deal__c (18 chars)
  bank_stmts INTEGER CHECK (bank_stmts IS NULL OR bank_stmts >= 0), -- how many bank statements were uploaded, 0 means it was skipped
  created_at TEXT DEFAULT (datetime('now')) CHECK (created_at IS NULL OR length(created_at) > 0),
  updated_at TEXT DEFAULT (datetime('now')) CHECK (updated_at IS NULL OR length(updated_at) > 0),
  started_at TEXT CHECK (started_at IS NULL OR length(started_at) > 0),
  submitted_at TEXT CHECK (submitted_at IS NULL OR length(submitted_at) > 0),
  signed_at TEXT CHECK (signed_at IS NULL OR length(signed_at) > 0),
  completed_at TEXT CHECK (completed_at IS NULL OR length(completed_at) > 0),
  edited_at TEXT CHECK (edited_at IS NULL OR length(edited_at) > 0)
);

-- Meta table for key-value storage
-- Used for configuration data like round-robin state, agents list, etc.
DROP TABLE IF EXISTS meta;

CREATE TABLE IF NOT EXISTS meta (
  `key` TEXT PRIMARY KEY,
  `value` TEXT NOT NULL,
  `created_at` TEXT DEFAULT (datetime('now')),
  `updated_at` TEXT DEFAULT (datetime('now'))
);

-- Insert default agents/round-robin data
INSERT OR IGNORE INTO
  meta (`key`, `value`)
VALUES
  (
    'agents:list',
    '[{"id":"005fL000003quxF","name":"Pre-Assigned Agent #1","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/35.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003qtxy","name":"Pre-Assigned Agent #2","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/20.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003qvA9","name":"Pre-Assigned Agent #3","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/generic.png","calendlyUrl":"https://calendly.com"}]'
  ),
  ('round-robin:index', '0'),
  (
    'round-robin:list',
    '[{"id":"005fL000003quxF","name":"Round Robin #1","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/35.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003qtxy","name":"Round Robin #2","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/20.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003qvA9","name":"Round Robin #3","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/generic.png","calendlyUrl":"https://calendly.com"}]'
  );
