const log = (...args) => console.log('[POSTMARK]', ...args);

/**
 * Send a Postmark email from template
 * @param {Object} env - Environment object containing POSTMARK_TOKEN
 * @param {Object} params
 * @param {string} [params.from="env.SENDER_EMAIL"] - Sender email address, defaults to env.SENDER_EMAIL
 * @param {string} params.to - Recipient email address
 * @param {string} params.template - Template alias in Postmark
 * @param {Object} params.placeholders - Template model data
 * @returns {Promise<Object>} - Response data from Postmark
 */
export async function sendEmailTemplate(env, { from = env.SENDER_EMAIL, to, template, placeholders, replyTo, attachments }) {
  log(JSON.stringify({ from, to, template, placeholders }));

  const body = {
    From: from,
    To: to,
    TemplateAlias: template,
    TemplateModel: placeholders,
  };

  if (replyTo) {
    body.ReplyTo = replyTo;
  }

  if (attachments) {
    body.Attachments = attachments;
  }

  const response = await fetch('https://api.postmarkapp.com/email/withTemplate', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'X-Postmark-Server-Token': env.POSTMARK_TOKEN,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  return data;
}
