import { createFactory } from 'hono/factory';
import { getApplicationByUUID } from '../../db/applications';
import { extractAndSanitizePIIFields } from '../../utils/helpers';
import { getAppBankStatements } from '../../kv';

const factory = createFactory();

export const verifyMigrationHandlers = factory.createHandlers(async (c) => {
  const env = c.env;
  const batchSize = parseInt(c.req.query('batchSize')) || 10;

  const verificationResults = await verifyRandomSample(env, batchSize);
  return c.json({ data: verificationResults });
});

async function verifySingleApplication(env, uuid) {
  try {
    const [kvApp, d1App] = await Promise.all([getApplicationFromKV(env, uuid), getApplicationByUUID(env, uuid)]);

    if (!kvApp && !d1App) {
      return {
        uuid,
        status: 'NOT_FOUND',
        message: 'Application not found in either KV or D1',
      };
    }

    if (!kvApp) {
      return {
        uuid,
        status: 'KV_MISSING',
        message: 'Application found in D1 but not in KV',
        d1Data: d1App,
      };
    }

    if (!d1App) {
      return {
        uuid,
        status: 'D1_MISSING',
        message: 'Application found in KV but not in D1',
        kvData: kvApp,
      };
    }

    const comparison = compareApplicationData(kvApp, d1App);

    return {
      uuid,
      status: comparison.isValid ? 'VERIFIED' : 'MISMATCH',
      message: comparison.isValid ? 'Data matches between KV and D1' : 'Data mismatch detected',
      differences: comparison.differences,
      kvData: kvApp,
      d1Data: d1App,
    };
  } catch (error) {
    return {
      uuid,
      status: 'ERROR',
      message: `Verification error: ${error.message}`,
      error: error.message,
    };
  }
}

async function verifyRandomSample(env, sampleSize) {
  try {
    const d1Statement = env.DB.prepare('SELECT uuid FROM applications ORDER BY RANDOM() LIMIT ?').bind(sampleSize);
    const d1Result = await d1Statement.all();
    const uuids = d1Result.results.map((row) => row.uuid);

    if (uuids.length === 0) {
      return {
        message: 'No applications found in D1',
        totalChecked: 0,
        results: [],
      };
    }

    const verificationPromises = uuids.map((uuid) => verifySingleApplication(env, uuid));
    const results = await Promise.all(verificationPromises);

    const summary = {
      totalChecked: results.length,
      verified: results.filter((r) => r.status === 'VERIFIED').length,
      mismatched: results.filter((r) => r.status === 'MISMATCH').length,
      kvMissing: results.filter((r) => r.status === 'KV_MISSING').length,
      d1Missing: results.filter((r) => r.status === 'D1_MISSING').length,
      errors: results.filter((r) => r.status === 'ERROR').length,
    };

    return {
      summary,
      results: results.filter((r) => r.status !== 'VERIFIED'), // Only show problematic ones
    };
  } catch (error) {
    return {
      status: 'ERROR',
      message: `Sample verification error: ${error.message}`,
      error: error.message,
    };
  }
}

async function getApplicationFromKV(env, uuid) {
  try {
    const application = await env.KV.get(`app:${uuid}`, 'json');
    if (!application) {
      return null;
    }

    if (application.applicationFields) {
      const { sanitizedApplicationFields } = extractAndSanitizePIIFields(application.applicationFields);
      application.applicationFields = sanitizedApplicationFields;
    }

    if (!application.status) {
      application.status = 'UNKNOWN';
    }

    if (!application.preQualifyFields && application.fields) {
      application.preQualifyFields = application.fields;
      delete application.fields;
    }

    try {
      const salesforceData = await env.KV.get(`app:${uuid}:salesforce`, { type: 'json' });
      if (salesforceData?.id) {
        application.salesforce_id = salesforceData.id;
      }
    } catch (error) {
      console.log(`Error getting Salesforce ID for ${uuid}:`, error.message);
    }

    try {
      const bankStatements = await getAppBankStatements(env, uuid);
      application.bank_stmts = bankStatements.length || null;
    } catch (error) {
      console.log(`Error getting bank statements for ${uuid}:`, error.message);
    }

    return application;
  } catch (error) {
    console.error(`Error getting application ${uuid} from KV:`, error.message);
    return null;
  }
}

function compareApplicationData(kvApp, d1App) {
  const differences = [];
  const fieldsToCompare = [
    'uuid',
    'version',
    'status',
    'domain',
    'approvalAmount',
    'reason',
    'fastTrack',
    'salesforce_id',
    'bank_stmts',
    'created_at',
    'updated_at',
    'started_at',
    'submitted_at',
    'signed_at',
    'completed_at',
  ];

  const jsonFieldsToCompare = ['preQualifyFields', 'applicationFields', 'pandadoc', 'utm', 'meta', 'agent'];

  for (const field of fieldsToCompare) {
    const kvValue = kvApp[field];
    const d1Value = d1App[field];

    if (field === 'fastTrack') {
      const kvBool = kvValue === true || kvValue === 1;
      const d1Bool = d1Value === true || d1Value === 1;
      if (kvBool !== d1Bool) {
        differences.push({
          field,
          kvValue,
          d1Value,
          type: 'boolean_mismatch',
        });
      }
    } else if (field === 'domain') {
      const kvDomain = kvValue || 'app.pinnaclefunding.com';
      const d1Domain = d1Value || 'app.pinnaclefunding.com';
      if (kvDomain !== d1Domain) {
        differences.push({
          field,
          kvValue: kvDomain,
          d1Value: d1Domain,
          type: 'domain_mismatch',
        });
      }
    } else if (kvValue !== d1Value) {
      if (!(kvValue == undefined && d1Value == null)) {
        differences.push({
          field,
          kvValue,
          d1Value,
          type: 'value_mismatch',
        });
      }
    }
  }

  for (const field of jsonFieldsToCompare) {
    const kvValue = kvApp[field];
    const d1Value = d1App[field];

    if (field === 'preQualifyFields' && !kvValue && kvApp.fields) {
      const kvJson = JSON.stringify(kvApp.fields);
      const d1Json = JSON.stringify(d1Value);
      if (kvJson !== d1Json) {
        differences.push({
          field: `${field} (legacy fields)`,
          kvValue: kvApp.fields,
          d1Value,
          type: 'json_mismatch',
        });
      }
    } else {
      if (!kvValue && !d1Value) {
        continue;
      }

      const kvJson = JSON.stringify(kvValue);
      const d1Json = JSON.stringify(d1Value);
      if (kvJson !== d1Json) {
        differences.push({
          field,
          kvValue,
          d1Value,
          type: 'json_mismatch',
        });
      }
    }
  }

  return {
    isValid: differences.length === 0,
    differences,
  };
}
