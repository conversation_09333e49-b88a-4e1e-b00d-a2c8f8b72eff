import { createFactory } from 'hono/factory';
import { sendToMigrationQueue } from '../../queues';

const factory = createFactory();

export const applicationMigrationHandlers = factory.createHandlers(async (c) => {
  const env = c.env;
  const batchSize = 50;

  const appUuids = await getAllApplicationUuidsFromKV(env);
  const totalApps = appUuids.length;
  console.log(`Found ${totalApps} applications to migrate`);

  const existingUuids = await getExistingUuidsFromD1(env);
  const uuidsToMigrate = appUuids.filter((uuid) => !existingUuids.includes(uuid));

  console.log(`${existingUuids.length} applications already migrated, ${uuidsToMigrate.length} remaining`);

  if (uuidsToMigrate.length === 0) {
    return c.json({
      message: 'All applications already migrated',
      data: {
        totalApps,
        alreadyMigrated: existingUuids.length,
        remaining: 0,
        batches: 0,
      },
    });
  }

  const batches = createBatches(uuidsToMigrate, batchSize);
  console.log(`Created ${batches.length} batches of ${batchSize} applications each`);

  let queuesSent = 0;
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    await sendToMigrationQueue(env, {
      uuids: batch,
      batchNumber: i + 1,
      totalBatches: batches.length,
    });
    queuesSent++;
  }

  return c.json({
    message: `Migration started - ${queuesSent} batches queued for processing`,
    data: {
      totalApps,
      alreadyMigrated: existingUuids.length,
      remaining: uuidsToMigrate.length,
      batches: batches.length,
      batchSize,
      queuesSent,
    },
  });
});

async function getAllApplicationUuidsFromKV(env) {
  const keys = await env.KV.list({ prefix: 'app:' });

  const appKeys = keys.keys.filter((key) => {
    const [, , subKey] = key.name.split(':');
    return !subKey;
  });

  return appKeys.map((key) => {
    const [_, uuid] = key.name.split(':');
    return uuid;
  });
}

async function getExistingUuidsFromD1(env) {
  const statement = env.DB.prepare(`SELECT uuid FROM applications`);
  const result = await statement.all();

  const existingUuids = result.results.map((row) => row.uuid);
  console.log(`Found ${existingUuids.length} applications already migrated`);
  return existingUuids;
}

function createBatches(array, batchSize) {
  const batches = [];
  for (let i = 0; i < array.length; i += batchSize) {
    batches.push(array.slice(i, i + batchSize));
  }
  return batches.slice(0, 1);
}
