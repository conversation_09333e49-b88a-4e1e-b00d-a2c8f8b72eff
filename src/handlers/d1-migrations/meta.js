import { createFactory } from 'hono/factory';
import { Agents } from '../../db/meta';
import { RoundRobin } from '../../round-robin';

const factory = createFactory();

const metaDataMigrationHandlers = factory.createHandlers(async (c) => {
  const env = c.env;

  const [agentsList, roundRobinList, roundRobinIndex] = await Promise.all([
    getAgentsListFromKV(env),
    getRoundRobinListFromKV(env),
    getRoundRobinIndexFromKV(env),
  ]);

  const migratedAgentsList = await migrateAgentsList(env, agentsList);
  const migratedRoundRobinList = await migrateRoundRobinList(env, roundRobinList);
  const migratedRoundRobinIndex = await migrateRoundRobinIndex(env, roundRobinIndex);

  return c.json({
    message: 'Meta data migration complete',
    data: {
      migratedAgentsList,
      migratedRoundRobinList,
      migratedRoundRobinIndex,
    },
  });
});

async function getAgentsListFromKV(env) {
  const agentsList = JSON.parse(await env.KV.get('agents:list')) || env.SAMPLE_AGENTS;
  return agentsList;
}

async function getRoundRobinListFromKV(env) {
  const agentsList = JSON.parse(await env.KV.get('round-robin:list')) || env.SAMPLE_AGENTS;
  return agentsList;
}

async function getRoundRobinIndexFromKV(env) {
  const index = await env.KV.get('round-robin:index');
  return parseInt(index) || 0;
}

async function migrateAgentsList(env, agentsList) {
  await Agents.setAgents(env, agentsList);
  return await Agents.getAgents(env);
}

async function migrateRoundRobinList(env, agentsList) {
  await RoundRobin.setAgents(env, agentsList);
  return await RoundRobin.getAgents(env);
}

async function migrateRoundRobinIndex(env, index) {
  await RoundRobin.setIndex(env, index);
  return await RoundRobin.getIndex(env);
}

export { metaDataMigrationHandlers };
