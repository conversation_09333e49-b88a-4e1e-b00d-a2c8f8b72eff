import { createFactory } from 'hono/factory';
import { zValidator } from '@hono/zod-validator';
import { getMeta } from '../../utils';
import { ensureApplicationByUUID } from './get-app';
import { bankStatementsSchema } from '../../schema/application';
import { cleanedApplication, AppError, extractAndSanitizePIIFields } from '../../utils/helpers';
import { updateApplication } from '../../db/applications';
import { sendToAdminQueue, sendToSalesforceQueue } from '../../queues';
import { putAppBankStatements } from '../../kv';

const factory = createFactory();

const validator = zValidator('json', bankStatementsSchema, async (result) => {
  if (!result.success) {
    throw new AppError('Validation Error: Invalid applicationFields', 400, 'validationError', result.error);
  }
});

export const completeAppHandlers = factory.createHandlers(validator, ensureApplicationByUUID, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  const application = c.get('application');
  const bankStatements = c.req.valid('json').bankStatements ?? [];
  const bank_stmts = bankStatements.length;
  const hasBankStatements = bank_stmts > 0;

  console.log('Uploaded', bank_stmts, 'bank statements');

  // Verify the application is in a state that can be completed
  if (application.status !== 'APP_SIGNED') {
    console.error(`Application status ${application.status} can't be completed`);
    throw new AppError(`Application can't be completed`, 400, 'completeApp', `Status is ${application.status}`);
  }

  const { sanitizedApplicationFields } = extractAndSanitizePIIFields(application.applicationFields);

  // Update application fields
  application.applicationFields = sanitizedApplicationFields;

  // Update status and timestamps
  application.completed_at = timestamp;
  application.status = 'APP_COMPLETED';
  application.meta.completed = getMeta(c.req.raw, timestamp);
  application.bank_stmts = bank_stmts;

  const promises = [
    updateApplication(c.env, uuid, application, timestamp),
    sendToAdminQueue(c.env, { application: { uuid: application.uuid, status: application.status } }),
    sendToSalesforceQueue(c.env, { application, hasBankStatements }),
  ];
  if (hasBankStatements) promises.push(putAppBankStatements(c.env, uuid, bankStatements));

  await Promise.all(promises);

  return c.json({ data: cleanedApplication(application) });
});
