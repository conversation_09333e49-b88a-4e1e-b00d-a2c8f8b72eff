import { createFactory } from 'hono/factory';
import { validator } from 'hono/validator';
import { preQualifySchema } from '../../schema/prequal';
import { z } from 'zod';
import { getMeta, isPrequalApproved } from '../../utils';
import { RoundRobin } from '../../round-robin';
import { updateApplication } from '../../db/applications';
import { AppError, safeUTMObject } from '../../utils/helpers';
import { sendToEmailQueue, sendToSalesforceQueue, sendToAdminQueue } from '../../queues';
import { Agents } from '../../db/meta';

const factory = createFactory();

const customValidator = validator('json', (value, c) => {
  const schema = z.object({
    preQualifyFields: preQualifySchema,
    utm: z.object({}).passthrough().optional(),
    domain: z.enum(c.env.ALLOWED_DOMAINS),
  });
  const parsed = schema.safeParse(value);
  if (!parsed.success) {
    throw new AppError('Validation Error: Invalid request data', 400, 'validationError', parsed.error);
  }
  return parsed.data;
});

export const createtAppHandlers = factory.createHandlers(customValidator, async (c) => {
  const { preQualifyFields, utm, domain } = c.req.valid('json');
  console.log('New App - Request Body:', { body: c.req.valid('json') });

  // Trim all string values in preQualifyFields
  Object.keys(preQualifyFields).forEach((key) => {
    if (typeof preQualifyFields[key] === 'string') {
      preQualifyFields[key] = preQualifyFields[key].trim();
    }
  });

  const safeUtm = safeUTMObject(utm);

  const timestamp = c.get('timestamp');
  const uuid = crypto.randomUUID();

  const { prequalApproved, reason, approvalAmount } = isPrequalApproved(preQualifyFields);
  const status = prequalApproved ? 'PREQUAL_APPROVED' : 'PREQUAL_DENIED';

  let agent;
  const agentsList = await Agents.getAgents(c.env);
  const utmRep = safeUtm?.utm_rep?.trim().toLowerCase();
  if (utmRep) console.log('utm_rep:', utmRep);

  const preAssignedAgent =
    utmRep === 'test'
      ? c.env.TEST_AGENT
      : agentsList.find((agent) => {
          const emailPrefix = agent.email.split('@')[0];
          return emailPrefix.toLowerCase() === utmRep;
        });

  if (preAssignedAgent) {
    agent = preAssignedAgent;
    console.log('Pre-assigned agent:', agent.name);
  } else if (prequalApproved) {
    agent = await RoundRobin.next(c.env);
    console.log('Round-robin assigned agent:', agent.name);
  }

  const application = {
    uuid,
    version: c.env.VERSION,
    status,
    created_at: timestamp,
    preQualifyFields: preQualifyFields,
    agent,
    utm: safeUtm,
    domain,
  };

  application.meta = { initiated: getMeta(c.req.raw, timestamp) };

  if (prequalApproved) {
    application.approvalAmount = approvalAmount;
  }

  if (!prequalApproved) {
    application.reason = reason;
  }

  await Promise.all([
    updateApplication(c.env, uuid, application, timestamp),
    sendToAdminQueue(c.env, { application }),
    sendToEmailQueue(c.env, { application }, { delaySeconds: c.env.EMAIL_QUEUE_DELAY }),
    sendToSalesforceQueue(c.env, { application }),
  ]);

  console.log('New App:', uuid, preQualifyFields.businessName, `(${status})`);

  return c.json({ uuid, status, agent, reason, approvalAmount }, 201);
});
