import { createFactory } from 'hono/factory';
import { validator } from 'hono/validator';
import { fastTrackPreQualifySchema } from '../../schema/prequal';
import { z } from 'zod';
import { getMeta } from '../../utils';
import { sendToSalesforceQueue, sendToAdminQueue } from '../../queues';
import { validateRep } from '../validate-rep';
import { updateApplication } from '../../db/applications';
import { Agents } from '../../db/meta';
import { AppError, safeUTMObject } from '../../utils/helpers';

const factory = createFactory();

const customValidator = validator('json', (value, c) => {
  const schema = z.object({
    preQualifyFields: fastTrackPreQualifySchema,
    utm: z.object({}).passthrough().optional(),
    domain: z.enum(c.env.ALLOWED_DOMAINS),
  });
  const parsed = schema.safeParse(value);
  if (!parsed.success) {
    throw new AppError('Validation Error: Invalid request data', 400, 'validationError', parsed.error);
  }
  return parsed.data;
});

export const createAppFasttrackHandlers = factory.createHandlers(customValidator, async (c) => {
  const { preQualifyFields, utm, domain } = c.req.valid('json');
  console.log('New App - Request Body:', { body: c.req.valid('json') });

  // Trim all string values in preQualifyFields
  Object.keys(preQualifyFields).forEach((key) => {
    if (typeof preQualifyFields[key] === 'string') {
      preQualifyFields[key] = preQualifyFields[key].trim();
    }
  });

  const safeUtm = safeUTMObject(utm);

  const timestamp = c.get('timestamp');
  const uuid = crypto.randomUUID();
  const status = 'PREQUAL_FAST_TRACK';
  const rep = safeUtm?.utm_rep?.trim().toLowerCase();

  console.log('utm_rep:', rep);

  const agentsList = await Agents.getAgents(c.env);
  const agent =
    rep === 'test'
      ? c.env.TEST_AGENT
      : agentsList.find((agent) => {
          const emailPrefix = agent.email.split('@')[0];
          return emailPrefix.toLowerCase() === rep;
        });

  const isValidRep = validateRep(agentsList, rep);

  if (!isValidRep || !agent) {
    throw new AppError('Validation Error: Invalid utm_rep', 400, 'validationError');
  }

  const application = {
    uuid,
    version: c.env.VERSION,
    status,
    created_at: timestamp,
    preQualifyFields,
    agent,
    utm: safeUtm,
    fastTrack: true,
    domain,
  };

  application.meta = { initiated: getMeta(c.req.raw, timestamp) };

  await Promise.all([
    updateApplication(c.env, uuid, application, timestamp),
    sendToAdminQueue(c.env, { application }),
    sendToSalesforceQueue(c.env, { application }),
  ]);

  console.log('New App:', uuid, preQualifyFields.businessName, `(${status})`);

  return c.json({ uuid, status, agent }, 201);
});
