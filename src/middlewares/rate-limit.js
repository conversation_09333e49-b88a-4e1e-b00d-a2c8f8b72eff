import { createMiddleware } from 'hono/factory';
import { env } from 'hono/adapter';

const IS_TESTING = env(import.meta) ? env(import.meta)?.MODE === 'test' : false;

export const rateLimitMiddleware = createMiddleware(async (c, next) => {
  // disable rate limit while running tests or in dev mode
  if (!IS_TESTING && !c.env.DEV_MODE) {
    const { success } = await c.env.RATELIMIT.limit({ key: `${c.header('cf-connecting-ip')}` });
    if (!success) return c.text('Too Many Requests', 429);
  }
  await next();
});
