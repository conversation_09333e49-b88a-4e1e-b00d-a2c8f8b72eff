import { AppError } from '../utils/helpers';

export async function putAppPII(env, uuid, data) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'putAppPII');
  if (!data) throw new AppError('Missing PII data', 400, 'putAppPII');
  data.version = env.VERSION;

  // 24 hours in seconds
  const expirationTtl = 24 * 60 * 60;

  await env.KV.put(`app:${uuid}:pii`, JSON.stringify(data), { expirationTtl });
  return data;
}

export async function getAppPII(env, uuid) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'getAppPII');

  let piiData = {};
  try {
    const result = await env.KV.get(`app:${uuid}:pii`, { type: 'json' });
    if (result) piiData = result;
  } catch (error) {
    console.error(`Error getting PII: ${error.message}`);
    piiData = {};
  }

  return piiData;
}

export async function putAppBankStatements(env, uuid, data) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'putAppBankStatements');
  if (!data) throw new AppError('Missing bank statements data', 400, 'putAppBankStatements');
  data.version = env.VERSION;

  const expirationTtl = env.APP_KV_TTL_DAYS * 60 * 60 * 24; // seconds

  await env.KV.put(`app:${uuid}:bank-statements`, JSON.stringify(data), { expirationTtl });
  return data;
}

export async function getAppBankStatements(env, uuid) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'getAppBankStatements');

  let bankStatements = [];
  try {
    const data = await env.KV.get(`app:${uuid}:bank-statements`, { type: 'json' });
    if (data && data.length) {
      bankStatements = data;
    }
  } catch (error) {
    console.error(`Error getting bank statements: ${error.message}`);
    bankStatements = [];
  }
  return bankStatements;
}
