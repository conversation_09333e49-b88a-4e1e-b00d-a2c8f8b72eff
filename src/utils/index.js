import { UAParser } from 'ua-parser-js';

export function isPrequalApproved({ fundingAmount, businessStartDate, monthlyRevenue, estimatedFICO }) {
  const reasons = [];

  if (new Date(businessStartDate) > new Date(new Date().setMonth(new Date().getMonth() - 6))) {
    reasons.push('start_date');
  }

  if (monthlyRevenue === '0-10000') {
    reasons.push('revenue');
  }
  // TODO: disabled for now at client request
  // if (estimatedFICO === '300-550') {
  //   reasons.push('credit_score');
  // }

  const prequalApproved = reasons.length === 0;
  const reason = reasons.length === 1 ? reasons[0] : reasons.length > 1 ? 'generic' : null;
  // round up to 5k
  const approvalAmount = Math.ceil(fundingAmount / 5000) * 5000;

  const prequalResult = {
    prequalApproved,
  };

  if (prequalApproved) {
    prequalResult.approvalAmount = approvalAmount;
  } else {
    prequalResult.reason = reason;
  }

  return prequalResult;
}

/**
 * Extracts meta information from request headers and Cloudflare geolocation data
 * @param {Request} request
 * @param {string} [timestamp=new Date().toISOString()] - Optional timestamp (defaults to current time)
 * @returns {Object} initiated metadata
 */
export function getMeta(request, timestamp = new Date().toISOString()) {
  const headers = Object.fromEntries(request.headers);
  const ip = headers['cf-connecting-ip'];
  const userAgent = UAParser(headers['user-agent']?.trim().slice(0, 1024));

  const meta = {
    timestamp,
    ip,
    ...Object.fromEntries(
      ['country', 'city', 'timezone', 'region', 'longitude', 'latitude', 'asOrganization'].map((k) => [k, request.cf?.[k]])
    ),
    userAgent,
  };

  return meta;
}
