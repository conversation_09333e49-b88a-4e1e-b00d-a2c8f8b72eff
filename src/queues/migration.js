import { extractAndSanitize<PERSON><PERSON>ields } from '../utils/helpers';
import { getAppBankStatements } from '../kv';

const log = (...args) => console.log('[MIGRATION]', ...args);

export async function migrationQueue<PERSON><PERSON>ler(message, env) {
  log('migration<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> called');

  try {
    const { uuids, batchNumber } = message;

    if (!uuids || !Array.isArray(uuids)) {
      console.error('No UUIDs array in migration message');
      return;
    }

    log(`Processing batch ${batchNumber} with ${uuids.length} applications`);

    const existingUuids = await getExistingUuidsFromD1(env, uuids);
    const uuidsToMigrate = uuids.filter((uuid) => !existingUuids.includes(uuid));

    if (uuidsToMigrate.length === 0) {
      log(`Batch ${batchNumber}: All ${uuids.length} applications already migrated, skipping`);
      return;
    }

    log(`Batch ${batchNumber}: Migrating ${uuidsToMigrate.length} of ${uuids.length} applications (${existingUuids.length} already exist)`);

    const applications = await getApplicationsFromKV(env, uuidsToMigrate);

    if (applications.length === 0) {
      log(`Batch ${batchNumber}: No applications found in KV for provided UUIDs`);
      return;
    }

    await migrateApplicationsBatch(env, applications);

    log(`Batch ${batchNumber}: Successfully migrated ${applications.length} applications`);
  } catch (error) {
    console.error(`Error processing migration batch: ${error.message}`);
    throw error;
  }
}

async function getExistingUuidsFromD1(env, uuids) {
  if (uuids.length === 0) return [];

  const placeholders = uuids.map(() => '?').join(',');
  const statement = env.DB.prepare(`SELECT uuid FROM applications WHERE uuid IN (${placeholders})`).bind(...uuids);
  const result = await statement.all();

  return result.results.map((row) => row.uuid);
}

async function getApplicationsFromKV(env, uuids) {
  const applications = await Promise.all(
    uuids.map(async (uuid) => {
      try {
        const application = await env.KV.get(`app:${uuid}`, 'json');
        if (!application) {
          log(`Application ${uuid} not found in KV`);
          return null;
        }

        if (application.applicationFields) {
          const { sanitizedApplicationFields } = extractAndSanitizePIIFields(application.applicationFields);
          application.applicationFields = sanitizedApplicationFields;
        }

        try {
          const salesforceData = await env.KV.get(`app:${uuid}:salesforce`, { type: 'json' });
          if (salesforceData?.id) {
            application.salesforce_id = salesforceData.id;
          }
        } catch (error) {
          log(`Error getting Salesforce ID for ${uuid}:`, error.message);
        }

        try {
          const bankStatements = await getAppBankStatements(env, uuid);
          application.bank_stmts = bankStatements.length || null;
        } catch (error) {
          log(`Error getting bank statements for ${uuid}:`, error.message);
        }

        return application;
      } catch (error) {
        log(`Error processing application ${uuid}:`, error.message);
        return null;
      }
    })
  );

  return applications.filter((app) => app !== null);
}

async function migrateApplicationsBatch(env, applications) {
  const statements = makeSqlInsertBatch(env, applications, 'applications', [
    'preQualifyFields',
    'applicationFields',
    'pandadoc',
    'utm',
    'meta',
    'agent',
  ]);

  await env.DB.batch(statements);
}

function makeSqlInsertBatch(env, applications, tableName, jsonCols = [], skipCols = []) {
  const appKeys = [
    'uuid',
    'version',
    'status',
    'domain',
    'preQualifyFields',
    'approvalAmount',
    'agent',
    'reason',
    'applicationFields',
    'pandadoc',
    'utm',
    'meta',
    'fastTrack',
    'salesforce_id',
    'bank_stmts',
    'created_at',
    'updated_at',
    'started_at',
    'submitted_at',
    'signed_at',
    'completed_at',
  ];

  const insertStatement = `INSERT INTO ${tableName} (${appKeys.join(',')}) VALUES (${appKeys.map(() => '?').join(',')})`;

  const statements = [];

  applications.forEach((app) => {
    const rowValues = appKeys.map((key) => {
      const val = app[key];

      if (key == 'domain' && !app.domain) {
        return 'app.pinnaclefunding.com';
      }

      if (key == 'preQualifyFields' && !app.preQualifyFields) {
        // Legacy applications with fields key
        return `${JSON.stringify(app.fields)}`;
      }

      if (key == 'status' && !app.status) {
        console.log('App with no status', app.uuid);
        return 'UNKNOWN';
      }

      if (key == 'fastTrack' && app.fastTrack == undefined) {
        return 0;
      }

      if (key == 'salesforce_id') {
        return val || null;
      }

      if (key === 'bank_stmts') {
        if (val === null || val === undefined) return null;
        return val;
      }

      if (jsonCols.includes(key)) {
        if (!val) return null;
        return `${JSON.stringify(val)}`;
      }

      if (val === null || val === '' || val === undefined || skipCols.includes(key)) {
        return null;
      }

      return `${String(val).replace(/'/g, '').replace(/"/g, "'")}`;
    });

    const statement = env.DB.prepare(insertStatement).bind(...rowValues);
    statements.push(statement);
  });

  return statements;
}
