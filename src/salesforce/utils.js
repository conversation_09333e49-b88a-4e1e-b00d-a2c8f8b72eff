export class SalesforceUtils {
  static getDealStage(status, hasBankStatements = true) {
    const statusToStage = {
      PREQUAL_APPROVED: 'Interested',
      PREQUAL_FAST_TRACK: 'Interested',
      PREQUAL_DENIED: 'Unqualified',
      APP_SUBMITTED: 'App Out',
      APP_SIGNED: 'App Signed',
      APP_COMPLETED: 'Application',
    };

    let stage;

    if (status === 'APP_COMPLETED' && !hasBankStatements) {
      stage = 'Waiting on Statements';
    } else {
      stage = statusToStage[status] || null;
    }

    console.log('[SalesforceUtils.getDealStage]', status, hasBankStatements, 'Stage:', stage);

    return stage;
  }

  static getCreditScore(estimatedFICO) {
    if (estimatedFICO === '300-550') return '<550';
    if (['700-780', '780-850'].includes(estimatedFICO)) return '700+';
    return estimatedFICO || null;
  }

  static maskFields(Deal__c) {
    const fields = ['EIN__c', 'SSN_O1__c', 'DOB_O1__c', 'SSN_O2__c', 'DOB_O2__c'];
    const mask = (v) => '*'.repeat(v.length - 2) + v.slice(-2);
    return {
      ...Deal__c,
      ...fields.reduce((acc, f) => {
        if (Deal__c.hasOwnProperty(f) && Deal__c[f] != null) acc[f] = mask(Deal__c[f]);
        return acc;
      }, {}),
    };
  }

  static printMeta(obj, path = '') {
    return Object.entries(obj).reduce((str, [key, val]) => {
      const fullKey = path ? `${path} → ${key}` : key;
      return (
        str + (val && typeof val === 'object' && !Array.isArray(val) ? this.printMeta(val, fullKey) : `<b>${fullKey}</b>: ${val}<br/>`)
      );
    }, '');
  }

  static parseMonthlyRevenue(monthlyRevenue) {
    if (!monthlyRevenue) return null;

    return monthlyRevenue
      .replace('+', '')
      .split('-')
      .reduce((a, b) => (+a + +b) / 2);
  }
}
