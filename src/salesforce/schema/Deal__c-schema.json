[{"label": "Record ID", "name": "Id", "type": "id", "custom": false, "length": 18, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner ID", "name": "OwnerId", "type": "reference", "custom": false, "length": 18, "referenceTo": ["Group", "User"], "relationshipName": "Owner", "picklistValues": []}, {"label": "Deleted", "name": "IsDeleted", "type": "boolean", "custom": false, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Deal Name", "name": "Name", "type": "string", "custom": false, "length": 80, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Created Date", "name": "CreatedDate", "type": "datetime", "custom": false, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Created By ID", "name": "CreatedById", "type": "reference", "custom": false, "length": 18, "referenceTo": ["User"], "relationshipName": "CreatedBy", "picklistValues": []}, {"label": "Last Modified Date", "name": "LastModifiedDate", "type": "datetime", "custom": false, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Last Modified By ID", "name": "LastModifiedById", "type": "reference", "custom": false, "length": 18, "referenceTo": ["User"], "relationshipName": "LastModifiedBy", "picklistValues": []}, {"label": "System Modstamp", "name": "SystemModstamp", "type": "datetime", "custom": false, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Last Activity Date", "name": "LastActivityDate", "type": "date", "custom": false, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Last Viewed Date", "name": "LastViewedDate", "type": "datetime", "custom": false, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Last Referenced Date", "name": "LastReferencedDate", "type": "datetime", "custom": false, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Requested Amount", "name": "Requested_Amount__c", "type": "currency", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Stage", "name": "Stage__c", "type": "picklist", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": [{"active": true, "defaultValue": true, "label": "New", "validFor": null, "value": "New"}, {"active": true, "defaultValue": false, "label": "Interested", "validFor": null, "value": "Interested"}, {"active": true, "defaultValue": false, "label": "Not Interested", "validFor": null, "value": "Not Interested"}, {"active": true, "defaultValue": false, "label": "Unqualified", "validFor": null, "value": "Unqualified"}, {"active": true, "defaultValue": false, "label": "Reconsider Later", "validFor": null, "value": "Reconsider Later"}, {"active": true, "defaultValue": false, "label": "Waiting on Statements", "validFor": null, "value": "Waiting on Statements"}, {"active": true, "defaultValue": false, "label": "App Out", "validFor": null, "value": "App Out"}, {"active": true, "defaultValue": false, "label": "Application", "validFor": null, "value": "Application"}, {"active": true, "defaultValue": false, "label": "Submitted", "validFor": null, "value": "Submitted"}]}, {"label": "Date Moved to Interested", "name": "Date_Moved_to_Interested__c", "type": "date", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Winning Rep", "name": "Winning_Rep__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Account", "name": "Account__c", "type": "reference", "custom": true, "length": 18, "referenceTo": ["Account"], "relationshipName": "Account__r", "picklistValues": []}, {"label": "Contact", "name": "Contact__c", "type": "reference", "custom": true, "length": 18, "referenceTo": ["Contact"], "relationshipName": "Contact__r", "picklistValues": []}, {"label": "Deal Reps", "name": "Deal_Reps__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "User is Rep", "name": "User_is_Rep__c", "type": "boolean", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business Legal Name", "name": "Business_Legal_Name__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business DBA Name", "name": "Business_DBA_Name__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business Street", "name": "Business_Street__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business City", "name": "Business_City__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business State", "name": "Business_State__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business Zip", "name": "Business_Zip__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Email", "name": "Email__c", "type": "email", "custom": true, "length": 80, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Phone", "name": "Phone__c", "type": "phone", "custom": true, "length": 40, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Legal Entity Type", "name": "Legal_Entity_Type__c", "type": "picklist", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": [{"active": true, "defaultValue": false, "label": "Corporation", "validFor": null, "value": "Corporation"}, {"active": true, "defaultValue": false, "label": "Sole Proprietorship", "validFor": null, "value": "Sole Proprietorship"}, {"active": true, "defaultValue": false, "label": "Nonprofit", "validFor": null, "value": "Nonprofit"}, {"active": true, "defaultValue": false, "label": "LLC", "validFor": null, "value": "LLC"}, {"active": true, "defaultValue": false, "label": "Partnership", "validFor": null, "value": "Partnership"}]}, {"label": "Industry", "name": "Industry__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business Start Date", "name": "Business_Start_Date__c", "type": "date", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "EIN", "name": "EIN__c", "type": "string", "custom": true, "length": 10, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Days in Interested", "name": "Days_in_Interested__c", "type": "double", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "First Name", "name": "First_Name_O1__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "First Name (O2)", "name": "First_Name_O2__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Last Name", "name": "Last_Name_O1__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Last Name (O2)", "name": "Last_Name_O2__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner <PERSON><PERSON>", "name": "Home_Zip_O1__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Home City (O2)", "name": "Home_City_O2__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Home Address (O2)", "name": "Home_Address_O2__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner State", "name": "Home_State_O1__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Home State (O2)", "name": "Home_State_O2__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner City", "name": "Home_City_O1__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner Address", "name": "Home_Address_O1__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Home Zip (O2)", "name": "Home_Zip_O2__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "% of Ownership (O2)", "name": "Percent_of_Ownership_O2__c", "type": "double", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner % of Ownership", "name": "Percent_of_Ownership_O1__c", "type": "double", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "DOB (O2)", "name": "DOB_O2__c", "type": "date", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "SSN (O2)", "name": "SSN_O2__c", "type": "string", "custom": true, "length": 11, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business Phone", "name": "Business_Phone__c", "type": "phone", "custom": true, "length": 40, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner SSN", "name": "SSN_O1__c", "type": "string", "custom": true, "length": 11, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Alt Phone", "name": "Alt_Phone__c", "type": "phone", "custom": true, "length": 40, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner DO<PERSON>", "name": "DOB_O1__c", "type": "date", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Received", "name": "Received__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Email (O2)", "name": "Email_O2__c", "type": "email", "custom": true, "length": 80, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Send Email on Submit", "name": "Send_Email_on_Submit__c", "type": "boolean", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Send Email After Time", "name": "Send_Email_After_Time__c", "type": "datetime", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Date/Time Submitted", "name": "Date_Time_Submitted__c", "type": "datetime", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner Credit Score", "name": "Credit_Score_Owner1__c", "type": "picklist", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": [{"active": true, "defaultValue": false, "label": "<550", "validFor": null, "value": "<550"}, {"active": true, "defaultValue": false, "label": "550-625", "validFor": null, "value": "550-625"}, {"active": true, "defaultValue": false, "label": "625-700", "validFor": null, "value": "625-700"}, {"active": true, "defaultValue": false, "label": "700+", "validFor": null, "value": "700+"}]}, {"label": "Credit Score (O2)", "name": "Credit_Score_Owner2__c", "type": "picklist", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": [{"active": true, "defaultValue": false, "label": "<550", "validFor": null, "value": "<550"}, {"active": true, "defaultValue": false, "label": "550-625", "validFor": null, "value": "550-625"}, {"active": true, "defaultValue": false, "label": "625-700", "validFor": null, "value": "625-700"}, {"active": true, "defaultValue": false, "label": "700+", "validFor": null, "value": "700+"}]}, {"label": "Num Deal Reps", "name": "Num_Deal_Reps__c", "type": "double", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business Revenue", "name": "Business_Revenue__c", "type": "currency", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Source", "name": "Source__c", "type": "picklist", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": [{"active": true, "defaultValue": false, "label": "LL", "validFor": null, "value": "LL"}, {"active": true, "defaultValue": false, "label": "SOS", "validFor": null, "value": "SOS"}, {"active": true, "defaultValue": false, "label": "App Portal", "validFor": null, "value": "App Portal"}, {"active": true, "defaultValue": false, "label": "JS", "validFor": null, "value": "JS"}]}, {"label": "Winning Rep User", "name": "Winning_Rep_User__c", "type": "reference", "custom": true, "length": 18, "referenceTo": ["User"], "relationshipName": "Winning_Rep_User__r", "picklistValues": []}, {"label": "Balances", "name": "Balances__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "App Status", "name": "App_Status__c", "type": "picklist", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": [{"active": true, "defaultValue": false, "label": "Declined But Fundable", "validFor": null, "value": "Declined But Fundable"}, {"active": true, "defaultValue": false, "label": "Declined", "validFor": null, "value": "Declined"}, {"active": true, "defaultValue": false, "label": "Approved", "validFor": null, "value": "Approved"}, {"active": true, "defaultValue": false, "label": "Contracts Out", "validFor": null, "value": "Contracts Out"}, {"active": true, "defaultValue": false, "label": "Contracts In", "validFor": null, "value": "Contracts In"}, {"active": true, "defaultValue": false, "label": "Funded", "validFor": null, "value": "Funded"}]}, {"label": "Last Task Date", "name": "Last_Task_Date__c", "type": "datetime", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Method", "name": "Method__c", "type": "picklist", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": [{"active": true, "defaultValue": false, "label": "Portal", "validFor": null, "value": "Portal"}, {"active": true, "defaultValue": false, "label": "Email", "validFor": null, "value": "Email"}, {"active": true, "defaultValue": false, "label": "Call", "validFor": null, "value": "Call"}]}, {"label": "Last Event Date", "name": "Last_Event_Date__c", "type": "datetime", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Last Activity Date", "name": "Last_Activity_Date__c", "type": "datetime", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "App Resume URL", "name": "App_Resume_URL__c", "type": "url", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "User is Related Deal Owner", "name": "User_is_Related_Deal_Owner__c", "type": "boolean", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "UUID", "name": "UUID__c", "type": "string", "custom": true, "length": 40, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Domain", "name": "Domain__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "FastTrack", "name": "FastTrack__c", "type": "boolean", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Estimated Fico", "name": "Estimated_Fico__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Monthly Revenue Range", "name": "Monthly_Revenue_Range__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Pre-Qual Amount", "name": "Pre_Qual_Amount__c", "type": "currency", "custom": true, "length": 0, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Funding Purpose", "name": "Funding_Purpose__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Top Priority", "name": "Top_Priority__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Funding Timeline", "name": "Funding_Timeline__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "UTM Source", "name": "UTM_Source__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "UTM Campaign", "name": "UTM_Campaign__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "UTM Site", "name": "UTM_Site__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "UTM Rep", "name": "UTM_Rep__c", "type": "string", "custom": true, "length": 20, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "IP", "name": "IP__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Country", "name": "Country__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "City", "name": "City__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Timezone", "name": "Timezone__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Browser", "name": "Browser__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "<PERSON><PERSON>", "name": "Device__c", "type": "string", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Website", "name": "Website__c", "type": "url", "custom": true, "length": 255, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Business Email", "name": "Business_Email__c", "type": "email", "custom": true, "length": 80, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner <PERSON><PERSON>", "name": "Email_O1__c", "type": "email", "custom": true, "length": 80, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Phone (O2)", "name": "Phone_O2__c", "type": "phone", "custom": true, "length": 40, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "UTM Affiliate", "name": "UTM_Affiliate__c", "type": "string", "custom": true, "length": 40, "referenceTo": [], "relationshipName": null, "picklistValues": []}, {"label": "Owner Phone", "name": "Phone_O1__c", "type": "phone", "custom": true, "length": 40, "referenceTo": [], "relationshipName": null, "picklistValues": []}]