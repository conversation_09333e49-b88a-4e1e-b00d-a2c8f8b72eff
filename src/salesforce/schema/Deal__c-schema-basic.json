[{"label": "Record ID", "name": "Id", "type": "id"}, {"label": "Owner ID", "name": "OwnerId", "type": "reference"}, {"label": "Deleted", "name": "IsDeleted", "type": "boolean"}, {"label": "Deal Name", "name": "Name", "type": "string"}, {"label": "Created Date", "name": "CreatedDate", "type": "datetime"}, {"label": "Created By ID", "name": "CreatedById", "type": "reference"}, {"label": "Last Modified Date", "name": "LastModifiedDate", "type": "datetime"}, {"label": "Last Modified By ID", "name": "LastModifiedById", "type": "reference"}, {"label": "System Modstamp", "name": "SystemModstamp", "type": "datetime"}, {"label": "Last Activity Date", "name": "LastActivityDate", "type": "date"}, {"label": "Last Viewed Date", "name": "LastViewedDate", "type": "datetime"}, {"label": "Last Referenced Date", "name": "LastReferencedDate", "type": "datetime"}, {"label": "Requested Amount", "name": "Requested_Amount__c", "type": "currency"}, {"label": "Stage", "name": "Stage__c", "type": "picklist"}, {"label": "Date Moved to Interested", "name": "Date_Moved_to_Interested__c", "type": "date"}, {"label": "Winning Rep", "name": "Winning_Rep__c", "type": "string"}, {"label": "Account", "name": "Account__c", "type": "reference"}, {"label": "Contact", "name": "Contact__c", "type": "reference"}, {"label": "Deal Reps", "name": "Deal_Reps__c", "type": "string"}, {"label": "User is Rep", "name": "User_is_Rep__c", "type": "boolean"}, {"label": "Business Legal Name", "name": "Business_Legal_Name__c", "type": "string"}, {"label": "Business DBA Name", "name": "Business_DBA_Name__c", "type": "string"}, {"label": "Business Street", "name": "Business_Street__c", "type": "string"}, {"label": "Business City", "name": "Business_City__c", "type": "string"}, {"label": "Business State", "name": "Business_State__c", "type": "string"}, {"label": "Business Zip", "name": "Business_Zip__c", "type": "string"}, {"label": "Email", "name": "Email__c", "type": "email"}, {"label": "Phone", "name": "Phone__c", "type": "phone"}, {"label": "Legal Entity Type", "name": "Legal_Entity_Type__c", "type": "picklist"}, {"label": "Industry", "name": "Industry__c", "type": "string"}, {"label": "Business Start Date", "name": "Business_Start_Date__c", "type": "date"}, {"label": "EIN", "name": "EIN__c", "type": "string"}, {"label": "Days in Interested", "name": "Days_in_Interested__c", "type": "double"}, {"label": "First Name", "name": "First_Name_O1__c", "type": "string"}, {"label": "First Name (O2)", "name": "First_Name_O2__c", "type": "string"}, {"label": "Last Name", "name": "Last_Name_O1__c", "type": "string"}, {"label": "Last Name (O2)", "name": "Last_Name_O2__c", "type": "string"}, {"label": "Owner <PERSON><PERSON>", "name": "Home_Zip_O1__c", "type": "string"}, {"label": "Home City (O2)", "name": "Home_City_O2__c", "type": "string"}, {"label": "Home Address (O2)", "name": "Home_Address_O2__c", "type": "string"}, {"label": "Owner State", "name": "Home_State_O1__c", "type": "string"}, {"label": "Home State (O2)", "name": "Home_State_O2__c", "type": "string"}, {"label": "Owner City", "name": "Home_City_O1__c", "type": "string"}, {"label": "Owner Address", "name": "Home_Address_O1__c", "type": "string"}, {"label": "Home Zip (O2)", "name": "Home_Zip_O2__c", "type": "string"}, {"label": "% of Ownership (O2)", "name": "Percent_of_Ownership_O2__c", "type": "double"}, {"label": "Owner % of Ownership", "name": "Percent_of_Ownership_O1__c", "type": "double"}, {"label": "DOB (O2)", "name": "DOB_O2__c", "type": "date"}, {"label": "SSN (O2)", "name": "SSN_O2__c", "type": "string"}, {"label": "Business Phone", "name": "Business_Phone__c", "type": "phone"}, {"label": "Owner SSN", "name": "SSN_O1__c", "type": "string"}, {"label": "Alt Phone", "name": "Alt_Phone__c", "type": "phone"}, {"label": "Owner DO<PERSON>", "name": "DOB_O1__c", "type": "date"}, {"label": "Received", "name": "Received__c", "type": "string"}, {"label": "Email (O2)", "name": "Email_O2__c", "type": "email"}, {"label": "Send Email on Submit", "name": "Send_Email_on_Submit__c", "type": "boolean"}, {"label": "Send Email After Time", "name": "Send_Email_After_Time__c", "type": "datetime"}, {"label": "Date/Time Submitted", "name": "Date_Time_Submitted__c", "type": "datetime"}, {"label": "Owner Credit Score", "name": "Credit_Score_Owner1__c", "type": "picklist"}, {"label": "Credit Score (O2)", "name": "Credit_Score_Owner2__c", "type": "picklist"}, {"label": "Num Deal Reps", "name": "Num_Deal_Reps__c", "type": "double"}, {"label": "Business Revenue", "name": "Business_Revenue__c", "type": "currency"}, {"label": "Source", "name": "Source__c", "type": "picklist"}, {"label": "Winning Rep User", "name": "Winning_Rep_User__c", "type": "reference"}, {"label": "Balances", "name": "Balances__c", "type": "string"}, {"label": "App Status", "name": "App_Status__c", "type": "picklist"}, {"label": "Last Task Date", "name": "Last_Task_Date__c", "type": "datetime"}, {"label": "Method", "name": "Method__c", "type": "picklist"}, {"label": "Last Event Date", "name": "Last_Event_Date__c", "type": "datetime"}, {"label": "Last Activity Date", "name": "Last_Activity_Date__c", "type": "datetime"}, {"label": "App Resume URL", "name": "App_Resume_URL__c", "type": "url"}, {"label": "User is Related Deal Owner", "name": "User_is_Related_Deal_Owner__c", "type": "boolean"}, {"label": "UUID", "name": "UUID__c", "type": "string"}, {"label": "Domain", "name": "Domain__c", "type": "string"}, {"label": "FastTrack", "name": "FastTrack__c", "type": "boolean"}, {"label": "Estimated Fico", "name": "Estimated_Fico__c", "type": "string"}, {"label": "Monthly Revenue Range", "name": "Monthly_Revenue_Range__c", "type": "string"}, {"label": "Pre-Qual Amount", "name": "Pre_Qual_Amount__c", "type": "currency"}, {"label": "Funding Purpose", "name": "Funding_Purpose__c", "type": "string"}, {"label": "Top Priority", "name": "Top_Priority__c", "type": "string"}, {"label": "Funding Timeline", "name": "Funding_Timeline__c", "type": "string"}, {"label": "UTM Source", "name": "UTM_Source__c", "type": "string"}, {"label": "UTM Campaign", "name": "UTM_Campaign__c", "type": "string"}, {"label": "UTM Site", "name": "UTM_Site__c", "type": "string"}, {"label": "UTM Rep", "name": "UTM_Rep__c", "type": "string"}, {"label": "IP", "name": "IP__c", "type": "string"}, {"label": "Country", "name": "Country__c", "type": "string"}, {"label": "City", "name": "City__c", "type": "string"}, {"label": "Timezone", "name": "Timezone__c", "type": "string"}, {"label": "Browser", "name": "Browser__c", "type": "string"}, {"label": "<PERSON><PERSON>", "name": "Device__c", "type": "string"}, {"label": "Website", "name": "Website__c", "type": "url"}, {"label": "Business Email", "name": "Business_Email__c", "type": "email"}, {"label": "Owner <PERSON><PERSON>", "name": "Email_O1__c", "type": "email"}, {"label": "Phone (O2)", "name": "Phone_O2__c", "type": "phone"}, {"label": "UTM Affiliate", "name": "UTM_Affiliate__c", "type": "string"}, {"label": "Owner Phone", "name": "Phone_O1__c", "type": "phone"}]