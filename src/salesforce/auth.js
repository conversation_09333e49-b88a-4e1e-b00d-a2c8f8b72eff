const log = (...args) => console.log('[SF AUTH]', ...args);

/**
 * Salesforce authentication module for Cloudflare Workers
 * Handles JWT-based authentication with Salesforce API
 */

const SF_TOKEN_KEY = 'sf:token';

/**
 * Generates a JWT token for Salesforce authentication
 * @param {string} consumerKey - Salesforce consumer key
 * @param {string} username - Salesforce username
 * @param {string} privateKey - Private key for signing the JWT
 * @param {number} expiresIn - Token expiration time in seconds (default: 3600 = 1 hour)
 * @returns {string} - JWT token
 */
async function generateJWT(consumerKey, username, privateKey, expiresIn = 3600) {
  const header = {
    alg: 'RS256',
  };

  const currentTime = Math.floor(Date.now() / 1000);
  const payload = {
    iss: consumerKey,
    sub: username,
    aud: 'https://login.salesforce.com',
    exp: currentTime + expiresIn,
    iat: currentTime,
  };

  // Encode header and payload
  const encodedHeader = btoa(JSON.stringify(header)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  const encodedPayload = btoa(JSON.stringify(payload)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

  // Create signature base
  const signatureBase = `${encodedHeader}.${encodedPayload}`;

  // Sign the JWT - this returns a Promise
  const textEncoder = new TextEncoder();
  const signatureBytes = await crypto.subtle.sign({ name: 'RSASSA-PKCS1-v1_5' }, privateKey, textEncoder.encode(signatureBase));

  // Convert signature to base64url
  const signature = btoa(String.fromCharCode(...new Uint8Array(signatureBytes)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');

  // Return complete JWT
  return `${signatureBase}.${signature}`;
}

function pemToArrayBuffer(pem) {
  const b64 = pem
    .replace(/-----BEGIN PRIVATE KEY-----/, '')
    .replace(/-----END PRIVATE KEY-----/, '')
    .replace(/\s+/g, '');
  const binary = atob(b64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * Authenticate with Salesforce using JWT flow
 * @param {Object} env - Environment variables
 * @returns {Promise<string>} - Access token
 */
// Uses activeToken for repeat requests to SF API (instead of fetching from KV on each API request)
let activeToken = null;
async function authenticateWithSalesforce(env) {
  if (activeToken && activeToken.expiresAt > Date.now()) {
    return activeToken;
  }

  const tokenData = await env.KV.get(SF_TOKEN_KEY, { type: 'json' });

  if (tokenData && tokenData.expiresAt > Date.now()) {
    log('Using KV cached token, expiresAt:', new Date(tokenData.expiresAt).toISOString());
    activeToken = {
      accessToken: tokenData.accessToken,
      instanceUrl: tokenData.instanceUrl,
      expiresAt: tokenData.expiresAt,
      cached: true,
    };
    return activeToken;
  }

  // Generate a new token
  const consumerKey = env.SALESFORCE_CONSUMER_KEY;
  const username = env.SALESFORCE_USERNAME;

  console.log('Authenticating with Salesforce:', username);

  if (!env.SALESFORCE_CONSUMER_KEY || !env.SALESFORCE_USERNAME || !env.SALESFORCE_PRIVATE_KEY) {
    console.error('[SF AUTH]', 'Missing ENV Consumer Key, Username or Private Key', { env });
  }

  // Import the private key
  const privateKeyData = pemToArrayBuffer(env.SALESFORCE_PRIVATE_KEY);
  const privateKey = await crypto.subtle.importKey(
    'pkcs8',
    privateKeyData,
    {
      name: 'RSASSA-PKCS1-v1_5',
      hash: { name: 'SHA-256' },
    },
    false,
    ['sign']
  );

  // Generate JWT
  const jwt = await generateJWT(consumerKey, username, privateKey);

  // Exchange JWT for access token
  const tokenResponse = await fetch('https://login.salesforce.com/services/oauth2/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
      assertion: jwt,
    }),
  });

  if (!tokenResponse.ok) {
    const errorData = await tokenResponse.json();
    console.error({ errorData });
    throw new Error(`Salesforce authentication failed: ${errorData.error} - ${errorData.error_description}`);
  }

  const tokenResult = await tokenResponse.json();

  // Store token in KV with expiration
  const expiresAt = Date.now() + 15 * 60 * 1000;
  await env.KV.put(
    SF_TOKEN_KEY,
    JSON.stringify({
      accessToken: tokenResult.access_token,
      instanceUrl: tokenResult.instance_url,
      expiresAt,
    }),
    { expirationTtl: 15 * 60 }
  );

  activeToken = {
    accessToken: tokenResult.access_token,
    instanceUrl: tokenResult.instance_url,
    expiresAt,
    cached: false,
  };
  return activeToken;
}

export { authenticateWithSalesforce };
