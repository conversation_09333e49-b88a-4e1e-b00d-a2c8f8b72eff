import { MetaD1 } from './db/meta';

const INDEX_KEY = 'round-robin:index';
const LIST_KEY = 'round-robin:list';

export class RoundRobin {
  static async getIndex(env) {
    const index = await MetaD1.get(env, INDEX_KEY);
    return parseInt(index) || 0;
  }

  static async setIndex(env, index) {
    if (typeof index !== 'number' || index < 0) {
      throw new Error('Index must be a non-negative integer');
    }

    // enforce integer (floor floats)
    const intIndex = Math.floor(index);

    await MetaD1.set(env, INDEX_KEY, intIndex.toString());
    return intIndex;
  }

  static async getAgents(env) {
    const agentsList = await MetaD1.get(env, LIST_KEY);
    return agentsList;
  }

  static async setAgents(env, agentsList) {
    if (!Array.isArray(agentsList)) {
      throw new Error('Agents list must be an array');
    }

    await MetaD1.set(env, LIST_KEY, agentsList);
    return agentsList;
  }

  static async next(env) {
    const agentsList = await this.getAgents(env);
    if (!agentsList?.length) {
      throw new Error('No agents available for round-robin assignment');
    }

    // must avoid race condition here
    const n = agentsList.length;
    const { idx } = (await env.DB.prepare(
      `UPDATE meta SET value = (CAST(value AS INTEGER) + 1) % ?
       WHERE key = ? RETURNING
         CASE
         WHEN CAST(value AS INTEGER) = 0 THEN
         ? - 1
         ELSE CAST(value AS INTEGER) - 1
         END AS idx`
    )
      .bind(n, INDEX_KEY, n)
      .first()) || { idx: 0 };

    console.log('RoundRobin.next', 'agents:', agentsList.map((o) => o.name).join(', '));
    console.log('RoundRobin.next', 'index:', idx);

    const agent = {
      ...agentsList[idx],
      assigned_at: new Date().toISOString(),
    };

    return agent;
  }

  static async previous(env) {
    const agentsList = await this.getAgents(env);
    if (!agentsList?.length) {
      throw new Error('No agents available for round-robin assignment');
    }

    const n = agentsList.length;
    const { idx } = (await env.DB.prepare(
      `UPDATE meta SET value = (CAST(value AS INTEGER) - 1 + ?) % ?
       WHERE key = ? RETURNING
         CASE
         WHEN CAST(value AS INTEGER) = ? - 1 THEN
         0
         ELSE CAST(value AS INTEGER) + 1
         END AS idx`
    )
      .bind(n, n, INDEX_KEY, n)
      .first()) || { idx: 0 };

    console.log('RoundRobin.previous', 'agents:', agentsList.map((o) => o.name).join(', '));
    console.log('RoundRobin.previous', 'index:', idx);

    const agent = {
      ...agentsList[idx],
      assigned_at: new Date().toISOString(),
    };

    return agent;
  }
}
