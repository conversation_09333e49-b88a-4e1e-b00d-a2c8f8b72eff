{"name": "app-sync-worker", "version": "4.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "deploy:dev": "wrangler deploy -e=dev", "dev": "wrangler dev -e=dev", "dev:remote": "wrangler dev -e=dev --x-remote-bindings", "migrate:local": "wrangler d1 execute app-db --local -e=dev --file=src/db/migrations/create_tables.sql", "migrate:dev": "wrangler d1 execute app-db --remote -e=dev --file=src/db/migrations/create_tables.sql -y", "test": "vitest run"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.7.8", "vitest": "~3.0.9", "wrangler": "^4.24.3"}, "dependencies": {"@hono/zod-validator": "^0.4.3", "hono": "^4.8.4", "ua-parser-js": "^2.0.4", "zod": "^3.25.76", "pdf-lib": "^1.17.1"}}