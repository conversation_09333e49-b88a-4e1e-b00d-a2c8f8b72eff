import { describe, it, expect } from 'vitest';
import { env } from 'cloudflare:test';

describe('D1 Database Setup', () => {
  it('should have applications table created', async () => {
    // Test that the table exists by running a simple query
    const result = await env.DB.prepare('SELECT name FROM sqlite_master WHERE type="table" AND name="applications"').first();
    
    expect(result).toBeDefined();
    expect(result.name).toBe('applications');
  });

  it('should be able to insert and retrieve data', async () => {
    // Test basic insert and select operations
    const testUuid = 'test-setup-uuid';
    
    await env.DB.prepare(`
      INSERT INTO applications (uuid, version, status, domain, preQualifyFields, fastTrack)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(testUuid, 1, 'TEST', 'test.com', '{}', 0).run();

    const result = await env.DB.prepare('SELECT * FROM applications WHERE uuid = ?').bind(testUuid).first();
    
    expect(result).toBeDefined();
    expect(result.uuid).toBe(testUuid);
    expect(result.status).toBe('TEST');
    
    // Clean up
    await env.DB.prepare('DELETE FROM applications WHERE uuid = ?').bind(testUuid).run();
  });
});
