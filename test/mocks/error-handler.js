// Mock implementation of the error handler for testing
export class AppError extends Error {
  constructor(message, statusCode = 400, source = 'appError', details = {}) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.source = source;
    this.details = details;
    this.timestamp = new Date().toISOString();

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      source: this.source,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack,
    };
  }
}

// Mock error handler for testing
export const mockErrorHandler = async (err, c) => {
  const errorId = 'TEST_ERROR_ID';

  if (err instanceof AppError) {
    return c.json({ errorId, error: err.message }, err.statusCode);
  } else {
    console.error(err);
    return c.json({ errorId, error: 'Internal Server Error' }, 500);
  }
};
