import { SELF } from 'cloudflare:test';
import { describe, it, expect } from 'vitest';
import { BASE_URL } from './setup';

describe('Middleware Tests', () => {
  describe('Body Size Limits', () => {
    it('rejects POST /app requests larger than 1MB', async () => {
      // Create a payload slightly larger than 1MB
      const largePayload = {
        data: 'x'.repeat(1024 * 1024 + 100), // 1MB + 100 bytes
      };

      const response = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(largePayload),
      });

      expect(response.status).toBe(413);
    });

    it('accepts larger payloads (up to 10MB) for the complete endpoint', async () => {
      // This test is simplified to just check the body size limit middleware
      // without going through the full application flow

      // Create a payload slightly larger than 1MB but less than 10MB
      const largePayload = {
        data: 'x'.repeat(2 * 1024 * 1024), // 2MB of data
      };

      // First try a regular endpoint which should reject it
      let response = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(largePayload),
      });

      // Should reject the payload since it's over 1MB
      expect(response.status).toBe(413);

      // Now create a mock complete endpoint URL (we don't need a real UUID)
      // The middleware should allow larger payloads for this endpoint pattern
      response = await SELF.fetch(`${BASE_URL}/app/mock-uuid/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(largePayload),
      });

      // Should not be a 413 error (might be 404 or 400 but not payload too large)
      expect(response.status).not.toBe(413);
    });
  });

  describe('CORS Middleware', () => {
    it('includes CORS headers in the response', async () => {
      // Use OPTIONS request to trigger CORS preflight response
      const response = await SELF.fetch(`${BASE_URL}/`, {
        method: 'OPTIONS',
        headers: {
          Origin: 'http://localhost:5173',
          'Access-Control-Request-Method': 'POST',
        },
      });

      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('http://localhost:5173');

      // For regular requests, we might not get the methods header
      // but for OPTIONS requests we should
      const methods = response.headers.get('Access-Control-Allow-Methods');
      if (methods) {
        expect(methods).toContain('GET');
        expect(methods).toContain('POST');
      } else {
        // If methods header is not present, at least verify we got a 200 response
        expect(response.status).toBe(204);
      }
    });
  });

  describe('Timestamp Middleware', () => {
    it('attaches a timestamp to the request context', async () => {
      // We can't directly test the context, but we can verify that timestamps are included in responses
      // Create an application which should include timestamps
      const prequalData = {
        preQualifyFields: {
          fundingAmount: 75000,
          purpose: 'Expansion',
          topPriority: 'cost',
          timeline: 'month',
          businessName: 'Test Business LLC',
          monthlyRevenue: '************',
          businessStartDate: '2020-01-01',
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          phone: '1234567890',
          estimatedFICO: '700-780',
          consent: true,
        },
        domain: 'app.pinnaclefunding.com',
      };

      const response = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(prequalData),
      });

      const data = await response.json();
      const appUuid = data.uuid;

      // Retrieve the application and check for timestamps
      const getResponse = await SELF.fetch(`${BASE_URL}/app/${appUuid}`);
      const appData = await getResponse.json();

      expect(appData.data).toHaveProperty('created_at');
      expect(appData.data).toHaveProperty('updated_at');

      // Verify timestamps are valid ISO strings
      expect(Date.parse(appData.data.created_at)).not.toBeNaN();
      expect(Date.parse(appData.data.updated_at)).not.toBeNaN();
    });
  });
});
