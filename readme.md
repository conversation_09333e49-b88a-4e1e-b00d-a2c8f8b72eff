# AppSync Worker API Documentation

The AppSync Worker is and API used to create, update, and retrieve prequal and applications.

## Base URL

```
https://appsync.pinnaclefundingco.com
```

---

## Endpoints

### POST `/app`

Create a new application - sent at time of submission of pre qualification.

#### Request Body

- `preQualifyFields` is required
- `utm` is optional (defaults to `null`)

```jsonc
{
  "preQualifyFields": {
    "fundingAmount": 75000,
    "purpose": "Expansion",
    "topPriority": "cost",
    "timeline": "month",
    "businessName": "BrightTech Solutions",
    "monthlyRevenue": "************",
    "businessStartDate": "2018-06-15",
    "firstName": "Alex",
    "lastName": "Morgan",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "estimatedFICO": "800-850",
    "consent": true
  },
  "utm": {
    "source": "google",
    "campaign": "spring_launch"
  }
}
```

#### Response Body

> **Note:** The `agent` is auto assigned based on a Round-Robin approach. The list of agents is stored in KV under `agents:list` and the next index for the round robin is `agents:index`.

```jsonc
{
  "uuid": "6fc93d18-730c-47ce-a628-91e02b844c6d",
  "agent": {
    "calendlyUrl": "https://calendly.com/alexgreenspan",
    "email": "<EMAIL>",
    "id": "00530000003xqAb",
    "image": "https://avatar.iran.liara.run/public/37",
    "name": "Alex Greenspan",
    "phone": "(*************",
    "assigned_at": "2025-04-03T07:27:50.317Z"
  }
}
```

---

### POST `/app/:uuid/submit`

Submit an application - sets status to `APP_SUBMITTED`, generates PandaDoc Session and stores the application fields. This endpoint also handles updates to existing applications, including those in `APP_EDITING` status.

#### Request Body

```jsonc
{
  "applicationFields": {
    "businessName": "Test LLC",
    "dbaName": "Doing business as",
    "website": "www.example.com",
    "entityType": "LLC",
    "ein": "111111111",
    "industry": "AutomotiveTruckingTransportation",
    "businessStartDate": "2020-03-01",
    "businessPhone": "(*************",
    "businessEmail": "<EMAIL>",
    "address": {
      "line1": "123 Melrose Street",
      "line2": "Suite 100",
      "city": "Brooklyn",
      "state": "NY",
      "zip": "11206"
    },
    "owners": [
      {
        "firstName": "Test",
        "lastName": "User",
        "dateOfBirth": "1994-10-30",
        "ssn": "111111111",
        "phone": "***********",
        "email": "<EMAIL>",
        "address": {
          "line1": "123 Melrose Street",
          "line2": "",
          "city": "Brooklyn",
          "state": "NY",
          "zip": "11206"
        },
        "ownershipPercentage": 60
      },
      {
        "firstName": "Test",
        "lastName": "User",
        "dateOfBirth": "1994-10-30",
        "ssn": "111111111",
        "phone": "***********",
        "email": "<EMAIL>",
        "address": {
          "line1": "123 Melrose Street",
          "line2": "",
          "city": "Brooklyn",
          "state": "OH",
          "zip": "11206"
        },
        "ownershipPercentage": 30
      }
    ],
    "currentStep": 0
  }
}
```

#### Response Body

```jsonc
{
  "data": {
    "uuid": "6fc93d18-730c-47ce-a628-91e02b844c6d",
    "version": 1,
    "status": "APP_SUBMITTED",
    "created_at": "2025-04-03T10:00:00.000Z",
    "updated_at": "2025-04-03T11:00:00.000Z",
    "preQualifyFields": {
      "fundingAmount": 75000,
      "purpose": "Expansion",
      "topPriority": "cost",
      "timeline": "month",
      "businessName": "BrightTech Solutions",
      "monthlyRevenue": "************",
      "businessStartDate": "2018-06-15",
      "firstName": "Alex",
      "lastName": "Morgan",
      "email": "<EMAIL>",
      "phone": "******-123-4567",
      "estimatedFICO": "800-850",
      "consent": true
    },
    "pandadoc": {
      "document" : {
        "id: "string",
      },
      "session": {
        "id: "string",
      }
    },
    "applicationFields": {
      "businessName": "Test LLC",
      "dbaName": "Doing business as",
      "website": "www.example.com",
      "entityType": "LLC",
      "ein": "111111111",
      "industry": "AutomotiveTruckingTransportation",
      "businessStartDate": "2020-03-01",
      "businessPhone": "(*************",
      "businessEmail": "<EMAIL>",
      "address": {
        "line1": "123 Melrose Street",
        "line2": "Suite 100",
        "city": "Brooklyn",
        "state": "NY",
        "zip": "11206"
      },
      "owners": [
        {
          "firstName": "Test",
          "lastName": "User",
          "dateOfBirth": "1994-10-30",
          "ssn": "111111111",
          "phone": "***********",
          "email": "<EMAIL>",
          "address": {
            "line1": "123 Melrose Street",
            "line2": "",
            "city": "Brooklyn",
            "state": "NY",
            "zip": "11206"
          },
          "ownershipPercentage": 60
        },
        {
          "firstName": "Test",
          "lastName": "User",
          "dateOfBirth": "1994-10-30",
          "ssn": "111111111",
          "phone": "***********",
          "email": "<EMAIL>",
          "address": {
            "line1": "123 Melrose Street",
            "line2": "",
            "city": "Brooklyn",
            "state": "OH",
            "zip": "11206"
          },
          "ownershipPercentage": 30
        }
      ],
      "currentStep": 0
    },
    "agent": {
      "calendlyUrl": "https://calendly.com/alexgreenspan",
      "email": "<EMAIL>",
      "id": "00530000003xqAb",
      "image": "https://avatar.iran.liara.run/public/37",
      "name": "Alex Greenspan",
      "phone": "(*************",
      "assigned_at": "2025-04-03T07:27:50.317Z"
    }
  }
}
```

---

### GET `/app/:uuid`

Retrieve an existing application - used for resuming an application where they left off.

#### Response Body

```jsonc
{
  "data": {
    "uuid": "6fc93d18-730c-47ce-a628-91e02b844c6d",
    "version": 1,
    "created_at": "2025-04-03T10:00:00.000Z",
    "updated_at": "2025-04-03T11:00:00.000Z",
    "preQualifyFields": {
      "fundingAmount": 75000,
      "purpose": "Expansion",
      "topPriority": "cost",
      "timeline": "month",
      "businessName": "BrightTech Solutions",
      "monthlyRevenue": "************",
      "businessStartDate": "2018-06-15",
      "firstName": "Alex",
      "lastName": "Morgan",
      "email": "<EMAIL>",
      "phone": "******-123-4567",
      "estimatedFICO": "800-850",
      "consent": true
    },
    "agent": {
      "calendlyUrl": "https://calendly.com/alexgreenspan",
      "email": "<EMAIL>",
      "id": "00530000003xqAb",
      "image": "https://avatar.iran.liara.run/public/37",
      "name": "Alex Greenspan",
      "phone": "(*************",
      "assigned_at": "2025-04-03T07:27:50.317Z"
    }
  }
}
```

---

### ~~PATCH `/app/:uuid`~~ (Deprecated)

> **Note:** This endpoint has been deprecated. Please use POST `/app/:uuid/submit` for both new submissions and updates.

For updating application fields, use the POST `/app/:uuid/submit` endpoint with the same request body format as shown above.
